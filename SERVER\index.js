require("dotenv").config();
const express = require("express");
const cookieParser = require("cookie-parser");
const cors = require("cors");
const testRouter = require("./routers/test_router");
const personnelRoutes = require("./routers/personnel");
const positionTitleRoutes = require("./routers/PositionTitle");
const departmentRouter = require("./routers/Department");
const employeeRouter = require("./routers/EmployeeList_router");
const statusRouter = require("./routers/Status");
const salaryRouter = require("./routers/Salary_router");
const mooeRoutes = require('./routers/mooeRoutes');
const fiscalYearRoutes = require("./routers/fiscalYearRoutes");
const personnelServicesRouter = require("./routers/personnelServices_router");
const excelUploadRouter = require("./routers/excelUpload");
const capitalOutlayRouter = require("./routers/capitalOutlayRouter");
const categoryRouter = require("./routers/category");
const proposalRouter = require("./routers/proposalRoutes");
const rataRoutes = require("./routers/rataRoutes");
const regionRouter = require("./routers/RegionRoutes");
const settings = require("./routers/settingsRoutes");
const annexRouter = require("./routers/annex");
const personnelServicesAnnexesRoutes = require("./routers/ps_annexes");
const setupRouter = require("./routers/setupRoutes");
const employeeCourtAppearanceRouter = require("./routers/employeeCourtAppearanceRoutes");
const subsistenceSTRouter = require("./routers/subsistenceAllowanceSTRoutes");
// const overtimeRouter = require("./routers/overtimeRoutes");
const overtimeRouter = require("./routers/overtimeRoutes");
const subsistenceAllowanceRouter = require("./routers/subsistenceAllowanceMDSRoutes");
const loyaltyPayRouter = require("./routers/loyaltyPayRoutes");
const reportsRouter = require("./routers/reports.routes");
const cosPersonnelRouter = require("./routers/COSPersonnel_router");
const proposalSummaryRoutes = require("./routers/proposalSummary");
//PARA SA MGA UPLOADER
const chartOfAccountsRouter = require("./routers/uploader/chartOfAccounts");
const uploadcategories = require("./routers/uploader/categories");
const uploadstatus = require("./routers/uploader/statuses");
const uploadregions = require("./routers/uploader/regions");  
const uploaddepartments = require("./routers/uploader/departments");
const uploadpositions = require("./routers/uploader/positionTitles");
const uploadsalaries = require("./routers/uploader/salaryGrades");
const uploadstatuses = require("./routers/uploader/statuses");
const uploadratas = require("./routers/uploader/ratas");
const mealAllowance = require("./routers/mealAllowanceRoutes")
const incomeCategoryRouter = require("./routers/income_category_router")
const incomeSubcategoryRouter = require("./routers/income_subcategory_router")
const statsRouter = require("./routers/statsRoutes");
const childrenAllowance = require("./routers/childrenAllowanceRoutes");
const medicalAllowanceRouter = require("./routers/medicalAllowanceRoutes");
const incomeRouter = require("./routers/incomeRoutes");
const discountSettingsRouter = require("./routes/discountSettings");
const retireeRouter = require("./routers/retireeRoutes");
const budgetarySupportRoutes = require("./routers/budgetarySupportRoutes");
require("./config/mongo_db")();

const app = express();

// MIDDLEWARE
app.use(
  cors({
    origin: [
      process.env.CLIENT1,
      process.env.CLIENT2,
      // list more allowed origins
    ],
    credentials: true,
  })
);
app.use(express.json());
app.use(cookieParser());

app.get("/", (req, res) => res.json({ message: "server is UP!" }));

app.use(testRouter);
app.use(employeeRouter);
app.use(statusRouter);
app.use(salaryRouter);
// Routes
app.use(personnelRoutes);
//Position Title Routes
app.use(positionTitleRoutes);
app.use(mooeRoutes);
app.use(fiscalYearRoutes);
//Department Title Routes
app.use(departmentRouter);
app.use(personnelServicesRouter);
app.use("/api", excelUploadRouter); // Excel Upload Route

app.use(capitalOutlayRouter);
app.use(categoryRouter);
app.use(proposalRouter);
app.use(rataRoutes);
app.use(settings);
app.use(regionRouter);
app.use(annexRouter);
app.use(personnelServicesAnnexesRoutes);
app.use(setupRouter);
app.use(employeeCourtAppearanceRouter);
app.use(overtimeRouter);
app.use(subsistenceAllowanceRouter);
app.use(subsistenceSTRouter);
app.use(loyaltyPayRouter);
app.use(reportsRouter);
app.use(cosPersonnelRouter);
app.use('/', mooeRoutes);

app.use(proposalSummaryRoutes);


//para sa mga uploader
app.use(chartOfAccountsRouter);
app.use(uploadcategories);
app.use(uploadstatus);
app.use(uploadregions);
app.use(uploaddepartments);
app.use(uploadpositions);
app.use(uploadsalaries);
app.use(uploadstatuses);
app.use(uploadratas);
app.use(mealAllowance);
app.use(incomeCategoryRouter);
app.use(incomeSubcategoryRouter);
app.use("/stats", statsRouter);
app.use(childrenAllowance);
app.use(medicalAllowanceRouter);
app.use(incomeRouter);
app.use(require("./routers/personnelServices_router_simplified"));
// app.use("/api", overtimeRouter);
// Add this line to use the new router
app.use(require("./routers/personnel_params_router"));

// Keep the original routers
app.use(require("./routers/personnelServices_router"));
// app.use(require("./routers/medicalAllowanceRoutes"));
app.use("/api/discount-settings", discountSettingsRouter);
app.use(retireeRouter);
app.use(budgetarySupportRoutes);
const userRegionAssignmentRouter = require('./routers/UserRegionAssignmentRoutes');
app.use(userRegionAssignmentRouter);
const userOrgAssignmentRouter = require('./routers/UserOrganizationalAssignmentRoutes');
app.use(userOrgAssignmentRouter);
const budgetManagerOrgRoutes = require('./routers/BudgetManagerOrgRoutes');
app.use(budgetManagerOrgRoutes);

app.listen(process.env.APP_PORT, () =>
  console.log(`🚀: http://localhost:${process.env.APP_PORT}`)
);
