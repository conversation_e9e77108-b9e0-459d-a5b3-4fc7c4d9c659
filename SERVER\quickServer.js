// Quick server to test MOOE fix
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5005;

// Middleware
app.use(cors({
  origin: ['http://localhost:3005', 'https://localhost:3005'],
  credentials: true
}));
app.use(express.json());

// Test endpoint
app.get('/test', (req, res) => {
  res.json({ message: 'Server is working!' });
});

// MOOE data endpoint with hardcoded data
app.get('/mooe-data', (req, res) => {
  console.log('🔧 MOOE endpoint called');
  
  try {
    // Hardcoded MOOE entries
    const entries = [
      {
        id: null,
        sublineItem: "Traveling Expenses",
        accountingTitle: "Traveling Expenses - Local",
        uacsCode: "5-02-01-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Training and Scholarship Expenses",
        accountingTitle: "Training Expenses",
        uacsCode: "5-02-02-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Office Supplies Expenses",
        uacsCode: "5-02-03-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Supplies and Materials Expenses",
        accountingTitle: "Fuel, Oil and Lubricants Expenses",
        uacsCode: "5-02-03-050",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Utilities Expenses",
        accountingTitle: "Water Expenses",
        uacsCode: "5-02-04-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Utilities Expenses",
        accountingTitle: "Electricity Expenses",
        uacsCode: "5-02-04-020",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Communication Expenses",
        accountingTitle: "Postage and Courier Services",
        uacsCode: "5-02-05-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Communication Expenses",
        accountingTitle: "Telephone Expenses",
        uacsCode: "5-02-05-020",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Communication Expenses",
        accountingTitle: "Internet Subscription Expenses",
        uacsCode: "5-02-05-030",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Repairs and Maintenance",
        accountingTitle: "Repairs and Maintenance - Motor Vehicles",
        uacsCode: "5-02-13-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Repairs and Maintenance",
        accountingTitle: "Repairs and Maintenance - Office Equipment",
        uacsCode: "5-02-13-020",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Advertising Expenses",
        uacsCode: "5-02-99-010",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Printing and Publication Expenses",
        uacsCode: "5-02-99-020",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Representation Expenses",
        uacsCode: "5-02-99-030",
        income: "0",
        subsidy: "0",
        amount: "0"
      },
      {
        id: null,
        sublineItem: "Other Maintenance and Operating Expenses",
        accountingTitle: "Other MOOE",
        uacsCode: "5-02-99-990",
        income: "0",
        subsidy: "0",
        amount: "0"
      }
    ];
    
    const response = {
      entries,
      status: "Draft",
      settings: {
        fiscalYear: "2026",
        budgetType: "Initial"
      }
    };
    
    console.log('📤 Sending MOOE response with', entries.length, 'entries');
    res.status(200).json(response);
    
  } catch (error) {
    console.error('❌ Error in MOOE endpoint:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// MOOE save endpoint (dummy)
app.post('/mooe-save', (req, res) => {
  console.log('💾 MOOE save called');
  res.status(200).json({ message: 'Data saved successfully!' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Quick MOOE server running at http://localhost:${PORT}`);
  console.log('📋 MOOE endpoint: http://localhost:' + PORT + '/mooe-data');
  console.log('🧪 Test endpoint: http://localhost:' + PORT + '/test');
});
