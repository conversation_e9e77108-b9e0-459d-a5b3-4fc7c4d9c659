const MooeProposal     = require('../models/mooeProposals');
const ChartOfAccounts  = require('../models/chartOfAccounts');
const EmployeeList     = require('../models/EmployeeList');
const COSPersonnel     = require('../models/COSPersonnel');
const Settings         = require('../models/Settings');

// New combined endpoint to fetch all data at once
exports.getMOOEData = async (req, res) => {
  try {
    console.log('🔧 getMOOEData called');

    // 1. Load active settings
    const activeSettings = await Settings.findOne({ isActive: true });
    console.log('Active settings found:', activeSettings);

    if (!activeSettings || activeSettings.fiscalYear == null) {
      console.log('❌ No active fiscal year found');
      return res.status(404).json({ error: 'No active fiscal year found' });
    }
    const activeFiscalYear = activeSettings.fiscalYear;
    console.log('✅ Active fiscal year:', activeFiscalYear);

    // 2. Fetch COA, MOOE proposals and COS personnel filtered by fiscal year
    console.log('🔍 Fetching data...');

    // First, let's check what Chart of Accounts data exists
    const totalCOA = await ChartOfAccounts.countDocuments();
    console.log('📊 Total Chart of Accounts records:', totalCOA);

    if (totalCOA > 0) {
      const sampleCOA = await ChartOfAccounts.findOne().lean();
      console.log('📋 Sample COA record:', {
        accountClass: sampleCOA.accountClass,
        lineItem: sampleCOA.lineItem,
        sublineItem: sampleCOA.sublineItem,
        uacsCode: sampleCOA.uacsCode
      });

      // Check different variations
      const expenseCount = await ChartOfAccounts.countDocuments({ accountClass: "Expense" });
      console.log('💰 Expense records:', expenseCount);

      const mooeCount = await ChartOfAccounts.countDocuments({
        lineItem: { $regex: /maintenance.*operating/i }
      });
      console.log('🔧 MOOE-like records:', mooeCount);
    }

    const [chartOfAccounts, mooeProposals, cosPersonnels] = await Promise.all([
      ChartOfAccounts.find({
        accountClass: "Expense",
        lineItem:    "Maintenance and Other Operating Expenses"
      }).lean(),

      MooeProposal.find({ fiscalYear: activeFiscalYear }).lean(),

      COSPersonnel.find({
        statusOfAppointment: "COS",
        fiscalYear: activeFiscalYear
      }).lean()
    ]);

    console.log('📊 Data fetched:');
    console.log('- Chart of Accounts:', chartOfAccounts.length);
    console.log('- MOOE Proposals:', mooeProposals.length);
    console.log('- COS Personnel:', cosPersonnels.length);

    // If no Chart of Accounts, create default MOOE entries
    if (chartOfAccounts.length === 0) {
      console.log('⚠️ No Chart of Accounts found! Creating default MOOE entries...');

      const defaultMOOEEntries = [
        {
          sublineItem: "Traveling Expenses",
          accountingTitle: "Traveling Expenses - Local",
          uacsCode: "5-02-01-010"
        },
        {
          sublineItem: "Training and Scholarship Expenses",
          accountingTitle: "Training Expenses",
          uacsCode: "5-02-02-010"
        },
        {
          sublineItem: "Supplies and Materials Expenses",
          accountingTitle: "Office Supplies Expenses",
          uacsCode: "5-02-03-010"
        },
        {
          sublineItem: "Utilities Expenses",
          accountingTitle: "Electricity Expenses",
          uacsCode: "5-02-04-020"
        },
        {
          sublineItem: "Communication Expenses",
          accountingTitle: "Telephone Expenses",
          uacsCode: "5-02-05-020"
        },
        {
          sublineItem: "Repairs and Maintenance",
          accountingTitle: "Repairs and Maintenance - Motor Vehicles",
          uacsCode: "5-02-13-010"
        },
        {
          sublineItem: "Other Maintenance and Operating Expenses",
          accountingTitle: "Other MOOE",
          uacsCode: "5-02-99-990"
        }
      ];

      // Use default entries as chartOfAccounts
      chartOfAccounts = defaultMOOEEntries;
      console.log('✅ Using default MOOE entries:', chartOfAccounts.length);
    }

    // 3. Compute total COS personnel amount
    const totalPersonnelAmount = cosPersonnels
      .reduce((sum, p) => sum + (p.Total || 0), 0);

    // 4. Build lookup for MOOE amounts
    const mooeMap = Object.fromEntries(
      mooeProposals.map(row => [row.uacsCode, row])
    );

    // 5. Determine submission status
    const status = mooeProposals.length > 0
      ? mooeProposals[0].status
      : "Not Submitted";

    // 6. Assemble entries array, including each proposal's _id as `id`
    const entries = chartOfAccounts.map(row => {
      const proposal = mooeMap[row.uacsCode];
      return {
        id:               proposal?._id?.toString() || null,
        sublineItem:      row.sublineItem,
        accountingTitle:  row.accountingTitle,
        uacsCode:         row.uacsCode,
        income:           proposal?.income?.toString() || "0",
        subsidy:          proposal?.subsidy?.toString() || "0",
        amount:           row.uacsCode === "5-02-99-990"
                          ? totalPersonnelAmount.toString()
                          : (proposal?.amount?.toString() || "0")
      };
    });

    // 7. Return payload
    const response = {
      entries,
      status,
      settings: {
        fiscalYear: activeFiscalYear,
        budgetType: activeSettings.budgetType
      }
    };

    console.log('📤 Sending response:');
    console.log('- Entries count:', response.entries.length);
    console.log('- Status:', response.status);
    console.log('- Settings:', response.settings);
    console.log('- Sample entry:', response.entries[0]);

    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching MOOE data', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};


// Keep existing endpoints for backward compatibility
exports.getMOOEEntries = async (req, res) => {
  try {
    // 1. Load active settings
    const activeSettings = await Settings.findOne({ isActive: true });
    if (!activeSettings || activeSettings.fiscalYear == null) {
      return res.status(404).json({ error: 'No active fiscal year found' });
    }
    const activeFiscalYear = activeSettings.fiscalYear;

    // 2. Fetch COA, MOOE proposals and COS personnel
    const [chartOfAccounts, mooeProposals, cosPersonnels] = await Promise.all([
      ChartOfAccounts.find({
        accountClass: "Expense",
        lineItem:    "Maintenance and Other Operating Expenses"
      }).lean(),

      MooeProposal.find({ fiscalYear: activeFiscalYear }).lean(),

      COSPersonnel.find({
        statusOfAppointment: "COS",
        fiscalYear: activeFiscalYear
      }).lean()
    ]);

    // 3. Compute total COS personnel amount
    const totalPersonnelAmount = cosPersonnels
      .reduce((sum, p) => sum + (p.Total || 0), 0);

    // 4. Build a lookup for MOOE proposals
    const mooeMap = Object.fromEntries(
      mooeProposals.map(row => [row.uacsCode, row])
    );

    // 5. Assemble the final array with `id`
    const data = chartOfAccounts.map(row => {
      const proposal = mooeMap[row.uacsCode];
      return {
        id:               proposal?._id?.toString() || null,
        sublineItem:      row.sublineItem,
        accountingTitle:  row.accountingTitle,
        uacsCode:         row.uacsCode,
        income:           proposal?.income?.toString() || "0",
        subsidy:          proposal?.subsidy?.toString() || "0",
        amount:           row.uacsCode === "5-02-99-990"
                          ? totalPersonnelAmount.toString()
                          : (proposal?.amount?.toString() || "0")
      };
    });

    return res.status(200).json(data);
  } catch (error) {
    console.error('Error fetching MOOE entries', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};


// Optimized bulk save for MOOE entries
exports.bulkSaveMOOE = async (req, res) => {
  try {
    const { meta, entries } = req.body;

    let processedEntries = [];
    if (entries && Array.isArray(entries)) {
      // Filter entries with non-zero and non-empty amounts
      processedEntries = entries.filter(item =>
        item.uacsCode &&
        ((item.income !== null && item.income !== undefined && item.income !== "" && parseFloat(item.income) !== 0) ||
         (item.subsidy !== null && item.subsidy !== undefined && item.subsidy !== "" && parseFloat(item.subsidy) !== 0))
      );
    } else if (Array.isArray(req.body)) {
      // Old format support
      processedEntries = req.body.flatMap(parentItem =>
        parentItem.children.filter(item =>
          item.uacsCode &&
          ((item.income !== null && item.income !== undefined && item.income !== "" && parseFloat(item.income) !== 0) ||
           (item.subsidy !== null && item.subsidy !== undefined && item.subsidy !== "" && parseFloat(item.subsidy) !== 0))
        )
      );
    }

    if (!processedEntries.length) {
      return res.status(400).json({ error: 'No valid entries to save' });
    }

    // Get the MooeProposal schema to check which fields are allowed
    const mooeProposalSchema = MooeProposal.schema.obj;
    const allowedFields = Object.keys(mooeProposalSchema);
    
    console.log('Allowed fields in schema:', allowedFields);

    const bulkOps = processedEntries.map(item => {
      // Create a filtered update object with only the fields that exist in the schema
      const updateObj = {};
      
      // Always include these core fields
      updateObj.income = item.income || 0;
      updateObj.subsidy = item.subsidy || 0;
      updateObj.amount = (Number(item.income) || 0) + (Number(item.subsidy) || 0);
      updateObj.uacsCode = item.uacsCode;
      
      // Only add other fields if they exist in the schema
      if (allowedFields.includes('date_process')) updateObj.date_process = item.date_process || new Date();
      if (allowedFields.includes('processBy')) updateObj.processBy = meta?.processBy || item.processBy;
      if (allowedFields.includes('processDate')) updateObj.processDate = meta?.processDate || item.processDate;
      if (allowedFields.includes('fiscalYear')) updateObj.fiscalYear = meta?.fiscalYear || item.fiscalYear;
      if (allowedFields.includes('budgetType')) updateObj.budgetType = meta?.budgetType || item.budgetType;
      if (allowedFields.includes('region')) updateObj.region = meta?.region || item.region;
      if (allowedFields.includes('status')) updateObj.status = meta?.status || item.status;
      if (allowedFields.includes('sublineItem')) updateObj.sublineItem = item.sublineItem;
      if (allowedFields.includes('accountingTitle')) updateObj.accountingTitle = item.accountingTitle;
      
      return {
        updateOne: {
          filter: { uacsCode: item.uacsCode },
          update: { $set: updateObj },
          upsert: true
        }
      };
    });

    await MooeProposal.bulkWrite(bulkOps);
    res.status(200).json({ message: 'Bulk save successful' });
  } catch (err) {
    console.error('Bulk save failed:', err);
    res.status(500).json({ error: 'Bulk save failed: ' + err.message });
  }
};

exports.getMOOEStatus = async (req, res) => {
  try {
    const activeSettings = await Settings.findOne({ isActive: true });
    if (!activeSettings) {
      return res.status(404).json({ error: 'No active fiscal year found' });
    }

    const activeFiscalYear = activeSettings.fiscalYear;
    const mooeProposal     = await MooeProposal.findOne({ fiscalYear: activeFiscalYear });

    if (mooeProposal) {
      return res.status(200).json({ status: mooeProposal.status });
    } else {
      return res.status(200).json({ status: "Not Submitted" });
    }
  } catch (error) {
    console.error('Error fetching MOOE Status:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// get mooe proposal by fiscal year and budget type
exports.getMooeProposalByFiscalYearAndBudgetType = async (req, res) => {
  try {
    const { fiscalYear, budgetType } = req.params;
    const mooeProposal = await MooeProposal.findOne({ fiscalYear, budgetType });
    if (!mooeProposal) {
      return res.status(404).json({ error: 'MOOE proposal not found' });
    }
    return res.status(200).json(mooeProposal);
  } catch (error) {
    console.error('Error fetching MOOE proposal:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

exports.getMooeList = async (req, res) => {
  try {
    const mooeProposals = await MooeProposal.find();
    res.status(200).json(mooeProposals);
  } catch (error) {
    console.error('Error fetching MOOE list:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

// delete mooe proposal// sa mooeController.js
exports.deleteAllMooeProposals = async (req, res) => {
  try {
    // (opsyonal) batay sa active fiscalYear lang:
    const activeSettings = await Settings.findOne({ isActive: true });
    const fy = activeSettings?.fiscalYear;

    // deleteMany kung gusto mo lahat ng docs, o filtered by fiscalYear:
    const filter = fy ? { fiscalYear: fy } : {};
    const result = await MooeProposal.deleteMany(filter);

    return res
      .status(200)
      .json({ message: `Deleted ${result.deletedCount} MOOE proposals.` });
  } catch (error) {
    console.error('Error deleting all MOOE proposals:', error);
    return res
      .status(500)
      .json({ error: 'Internal Server Error' });
  }
};

// Update the create/update functions to handle income and subsidy
exports.createMOOE = async (req, res) => {
  try {
    const { income, subsidy, ...otherData } = req.body;
    
    // Calculate amount as sum of income and subsidy
    const amount = (Number(income) || 0) + (Number(subsidy) || 0);
    
    const mooeData = {
      ...otherData,
      income,
      subsidy,
      amount,
    };
    
    const mooe = new MooeProposal(mooeData);
    await mooe.save();
    
    res.status(201).json(mooe);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

exports.updateMOOE = async (req, res) => {
  try {
    const { income, subsidy, ...otherData } = req.body;
    
    // Calculate amount as sum of income and subsidy
    const amount = (Number(income) || 0) + (Number(subsidy) || 0);
    
    const updatedData = {
      ...otherData,
      income,
      subsidy,
      amount,
    };
    
    const mooe = await MooeProposal.findByIdAndUpdate(
      req.params.id,
      updatedData,
      { new: true }
    );
    
    if (!mooe) {
      return res.status(404).json({ message: 'MOOE not found' });
    }
    
    res.status(200).json(mooe);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};
