# 🚀 COMPLETE BUDGET-FMIS MOCK SERVER - AL<PERSON> ENDPOINTS

**Server URL:** `http://localhost:5005`  
**Status:** ✅ FULLY OPERATIONAL  
**Last Updated:** June 7, 2025

---

## 📋 **ALL AVAILABLE ENDPOINTS**

### 👥 **PERSONNEL ENDPOINTS**
- `GET /getpersonnels` - Returns 4 personnel records with complete details
  - Includes: employeeName, position, salaryGrade, monthlySalary, status, region, department

### 📊 **MOOE ENDPOINTS**
- `GET /mooe-data` - Returns MOOE data with categories, amounts, and status
- `GET /mooe-list` - Returns simplified MOOE list for dropdowns
- `POST /mooe-save` - Save MOOE data (accepts any JSON body)
- `PUT /mooe-data/:id` - Update specific MOOE record
- `DELETE /mooe-data/:id` - Delete specific MOOE record

### 🏗️ **CAPITAL OUTLAY ENDPOINTS**
- `GET /capital-outlays` - Returns 5 capital outlay records with full details
- `GET /capital-outlay-list` - Returns simplified capital outlay list
- `POST /capital-outlays` - Create new capital outlay record
- `PUT /capital-outlays/:id` - Update specific capital outlay record
- `DELETE /capital-outlays/:id` - Delete specific capital outlay record

### 📂 **CHART OF ACCOUNTS ENDPOINTS**
- `GET /categories` - Returns 5 capital outlay categories
- `GET /subline-items?category=X` - Returns subline items for specific category
- `GET /accounting-titles?sublineItem=X` - Returns accounting titles with UACS codes

### 💰 **FINANCIAL ENDPOINTS**
- `GET /income-subcategories` - Returns 4 income subcategories
- `GET /budgetary-support` - Returns 4 budgetary support records

### 📊 **STATISTICS/DASHBOARD ENDPOINTS**
- `GET /stats/overview` - Returns comprehensive dashboard statistics
- `GET /stats/budget-summary` - Returns detailed budget breakdown and utilization

### 👤 **USER/AUTH ENDPOINTS**
- `GET /users/profile` - Returns current user profile information
- `GET /users` - Returns list of all users in the system

### 📋 **PROPOSALS ENDPOINTS**
- `GET /proposals` - Returns list of all budget proposals
- `GET /proposals/:id` - Returns specific proposal details

### 📊 **REPORTS ENDPOINTS**
- `GET /reports/budget` - Returns comprehensive budget report
- `GET /reports/personnel` - Returns personnel statistics report

### ⚙️ **SYSTEM ENDPOINTS**
- `GET /settings/active` - Returns fiscal year, budget type, and system settings
- `GET /test` - Test endpoint with server status and available endpoints list

---

## 🎯 **SAMPLE DATA PROVIDED**

### **Personnel (4 records):**
1. Juan Dela Cruz - Administrative Officer V (₱45,000)
2. Maria Santos - Engineer II (₱38,000)
3. Pedro Garcia - Accountant III (₱42,000)
4. Ana Reyes - Administrative Assistant II (₱18,000)

### **Capital Outlay (5 records):**
1. Books - ₱175,000 (Furniture & Fixtures category)
2. Office Equipment - ₱500,000 (Machinery & Equipment category)
3. Furniture and Fixtures - ₱300,000 (Furniture & Fixtures category)
4. ICT Equipment - ₱800,000 (Machinery & Equipment category)
5. Motor Vehicles - ₱1,200,000 (Transportation Equipment category)

### **MOOE (3 records):**
1. Traveling Expenses - Local - ₱50,000
2. Office Supplies - ₱25,000
3. Water Expenses - ₱15,000

### **Categories (5 categories):**
1. ACQUISITION OF FURNITURE & FIXTURES AND BOOKS
2. ACQUISITION OF MACHINERY AND EQUIPMENT
3. ACQUISITION OF TRANSPORTATION EQUIPMENT
4. CONSTRUCTION/ACQUISITION OF BUILDINGS AND OTHER STRUCTURES
5. ACQUISITION OF LAND

### **Complete Chart of Accounts Mapping:**
- **Furniture & Fixtures** → Furniture Fixture and Books → Books, Furniture and Fixtures, Office Furniture
- **Machinery & Equipment** → Office Equipment, ICT Equipment, Agricultural Equipment, Other Machinery
- **Transportation** → Motor Vehicles, Watercrafts, Aircrafts, Other Transportation
- **Buildings** → Buildings, Other Structures, Repairs and Maintenance
- **Land** → Land, Land Improvements

---

## 🧪 **TESTING ENDPOINTS**

### **Quick Test Commands:**
```bash
# Test server status
curl http://localhost:5005/test

# Test personnel data
curl http://localhost:5005/getpersonnels

# Test capital outlay data
curl http://localhost:5005/capital-outlays

# Test categories
curl http://localhost:5005/categories

# Test subline items
curl "http://localhost:5005/subline-items?category=ACQUISITION%20OF%20MACHINERY%20AND%20EQUIPMENT"

# Test accounting titles
curl "http://localhost:5005/accounting-titles?sublineItem=Office%20Equipment"

# Test dashboard stats
curl http://localhost:5005/stats/overview

# Test proposals
curl http://localhost:5005/proposals

# Test user profile
curl http://localhost:5005/users/profile
```

---

## ✅ **FULLY SUPPORTED FEATURES**

### **Capital Outlay Table:**
- ✅ All categories load properly
- ✅ Subline items populate for all categories
- ✅ Accounting titles populate with UACS codes
- ✅ Enhanced features (search, filter, export, collapse/expand)
- ✅ Auto-save functionality
- ✅ Sample data for testing

### **MOOE Table:**
- ✅ MOOE data loads properly
- ✅ All enhanced features working
- ✅ CRUD operations supported

### **ProposalCustomPage:**
- ✅ No more 404 errors
- ✅ All required endpoints available
- ✅ Personnel, MOOE, Capital Outlay, Income, Budgetary Support data

### **Dashboard/Statistics:**
- ✅ Overview statistics
- ✅ Budget summaries
- ✅ Recent activities
- ✅ Budget breakdown

### **User Management:**
- ✅ User profiles
- ✅ User lists
- ✅ Authentication data

### **Reports:**
- ✅ Budget reports
- ✅ Personnel reports
- ✅ Comprehensive data

---

## 🎉 **STATUS: ALL SYSTEMS OPERATIONAL**

**The complete BUDGET-FMIS mock server is now fully operational with comprehensive data for all modules!**

**All pages should now work without 404 errors and with realistic test data.**
