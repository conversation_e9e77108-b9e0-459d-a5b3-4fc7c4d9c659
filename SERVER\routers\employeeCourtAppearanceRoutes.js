const {
  getAllAppearances,
  addAppearance,
  editAppearance,
  deleteAppearance,
  getSumOfCourtAppearanceAmount,
} = require("../controllers/employeeCourtAppearanceController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const appearanceRouter = Router();

// 🔒 SECURED ROUTES

appearanceRouter.get("/court-appearances", ...authenticatedRoute(), getAllAppearances);
appearanceRouter.post("/court-appearances", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), addAppearance);
appearanceRouter.put("/court-appearances/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), editAppearance);
appearanceRouter.delete("/court-appearances/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteAppearance);
appearanceRouter.get("/court-appearances/sum", ...authenticatedRoute(), getSumOfCourtAppearanceAmount);

module.exports = appearanceRouter;
