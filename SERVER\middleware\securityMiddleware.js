const checkToken = require('./check_token');
const checkDueDate = require('./checkDueDate');
const checkRegionAccess = require('./checkRegionAccess');

// Permission levels constants
const PERMISSION_LEVELS = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  BUDGET_MANAGER: 'BUDGET MANAGER',
  BUDGET_OFFICER: 'BUDGET OFFICER',
  SUPER_ADMIN: 'SUPER ADMIN'
};

// Role checking helper function
const hasRequiredRole = (userRoles, requiredRoles) => {
  if (!userRoles) return false;
  
  // Handle both array and string roles
  const roles = Array.isArray(userRoles) ? userRoles : [userRoles];
  const required = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  return roles.some(role => required.includes(role));
};

// Basic authentication middleware
const authenticatedRoute = () => {
  return [checkToken];
};

// Admin-only access middleware
const adminRoute = () => {
  return [
    checkToken,
    (req, res, next) => {
      const adminRoles = [
        PERMISSION_LEVELS.ADMIN,
        PERMISSION_LEVELS.BUDGET_MANAGER,
        PERMISSION_LEVELS.BUDGET_OFFICER,
        PERMISSION_LEVELS.SUPER_ADMIN
      ];
      
      if (!hasRequiredRole(req.user?.Roles, adminRoles)) {
        return res.status(403).json({ 
          message: "Access denied. Admin privileges required." 
        });
      }
      
      next();
    }
  ];
};

// Due date protected route with permission level
const dueDateProtectedRoute = (permissionLevel = PERMISSION_LEVELS.USER) => {
  const middleware = [checkToken, checkDueDate];
  
  // Add role checking if permission level is specified
  if (permissionLevel !== PERMISSION_LEVELS.USER) {
    middleware.push((req, res, next) => {
      let requiredRoles = [];
      
      switch (permissionLevel) {
        case PERMISSION_LEVELS.ADMIN:
          requiredRoles = [
            PERMISSION_LEVELS.ADMIN,
            PERMISSION_LEVELS.BUDGET_MANAGER,
            PERMISSION_LEVELS.BUDGET_OFFICER,
            PERMISSION_LEVELS.SUPER_ADMIN
          ];
          break;
        case PERMISSION_LEVELS.BUDGET_MANAGER:
          requiredRoles = [
            PERMISSION_LEVELS.BUDGET_MANAGER,
            PERMISSION_LEVELS.SUPER_ADMIN
          ];
          break;
        case PERMISSION_LEVELS.BUDGET_OFFICER:
          requiredRoles = [
            PERMISSION_LEVELS.BUDGET_OFFICER,
            PERMISSION_LEVELS.BUDGET_MANAGER,
            PERMISSION_LEVELS.SUPER_ADMIN
          ];
          break;
        case PERMISSION_LEVELS.SUPER_ADMIN:
          requiredRoles = [PERMISSION_LEVELS.SUPER_ADMIN];
          break;
        default:
          requiredRoles = [permissionLevel];
      }
      
      if (!hasRequiredRole(req.user?.Roles, requiredRoles)) {
        return res.status(403).json({ 
          message: `Access denied. ${permissionLevel} privileges required.` 
        });
      }
      
      next();
    });
  }
  
  return middleware;
};

// Region access protected route
const regionProtectedRoute = () => {
  return [checkToken, checkRegionAccess];
};

module.exports = {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  regionProtectedRoute,
  PERMISSION_LEVELS,
  hasRequiredRole
};
