const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5005;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3005', 'https://localhost:3005'],
  credentials: true
}));
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// ==================== PERSONNEL ENDPOINTS ====================
app.get('/getpersonnels', (req, res) => {
  console.log('👥 Personnel endpoint called');
  
  const personnels = [
    {
      _id: "p1",
      employeeName: "Juan Dela Cruz",
      position: "Administrative Officer V",
      salaryGrade: "18",
      stepIncrement: "1",
      monthlySalary: "45000",
      status: "Permanent",
      region: "Region IV-A",
      department: "Administrative Division"
    },
    {
      _id: "p2",
      employeeName: "Maria Santos",
      position: "Engineer II",
      salaryGrade: "15",
      stepIncrement: "3",
      monthlySalary: "38000",
      status: "Permanent",
      region: "Region IV-A",
      department: "Engineering Division"
    },
    {
      _id: "p3",
      employeeName: "Pedro Garcia",
      position: "Accountant III",
      salaryGrade: "18",
      stepIncrement: "2",
      monthlySalary: "42000",
      status: "Permanent",
      region: "Region IV-A",
      department: "Finance Division"
    },
    {
      _id: "p4",
      employeeName: "Ana Reyes",
      position: "Administrative Assistant II",
      salaryGrade: "6",
      stepIncrement: "4",
      monthlySalary: "18000",
      status: "Casual",
      region: "Region IV-A",
      department: "Administrative Division"
    }
  ];
  
  console.log('📤 Sending', personnels.length, 'personnel records');
  res.status(200).json({ personnels });
});

// ==================== MOOE ENDPOINTS ====================
app.get('/mooe-data', (req, res) => {
  console.log('📋 MOOE Data endpoint called');
  
  const mooeData = [
    {
      _id: "mooe1",
      category: { _id: "cat1", categoryName: "Traveling Expenses" },
      subCategory: "Traveling Expenses - Local",
      accountingTitle: "Traveling Expenses - Local",
      uacsCode: "5-02-01-010",
      particulars: "Local travel for official business",
      amount: "50000",
      status: "Not Submitted"
    },
    {
      _id: "mooe2",
      category: { _id: "cat2", categoryName: "Supplies and Materials" },
      subCategory: "Office Supplies",
      accountingTitle: "Office Supplies Expenses",
      uacsCode: "5-02-03-010",
      particulars: "Office supplies and materials",
      amount: "25000",
      status: "Not Submitted"
    },
    {
      _id: "mooe3",
      category: { _id: "cat3", categoryName: "Utilities" },
      subCategory: "Water Expenses",
      accountingTitle: "Water Expenses",
      uacsCode: "5-02-05-010",
      particulars: "Water utility expenses",
      amount: "15000",
      status: "Not Submitted"
    }
  ];
  
  console.log('📤 Sending', mooeData.length, 'MOOE records');
  res.status(200).json({ 
    mooeData,
    status: "Draft",
    totalAmount: mooeData.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
  });
});

app.get('/mooe-list', (req, res) => {
  console.log('📋 MOOE List endpoint called');
  
  const mooeList = [
    {
      _id: "m1",
      category: "Traveling Expenses",
      subCategory: "Traveling Expenses - Local",
      accountingTitle: "Traveling Expenses - Local",
      uacsCode: "5-02-01-010",
      amount: "50000"
    },
    {
      _id: "m2", 
      category: "Supplies and Materials",
      subCategory: "Office Supplies",
      accountingTitle: "Office Supplies Expenses",
      uacsCode: "5-02-03-010",
      amount: "25000"
    },
    {
      _id: "m3",
      category: "Utilities",
      subCategory: "Water Expenses", 
      accountingTitle: "Water Expenses",
      uacsCode: "5-02-05-010",
      amount: "15000"
    }
  ];
  
  console.log('📤 Sending', mooeList.length, 'MOOE list records');
  res.status(200).json({ mooeList });
});

// ==================== CAPITAL OUTLAY ENDPOINTS ====================
app.get('/capital-outlays', (req, res) => {
  console.log('🏗️ Capital Outlays endpoint called');
  
  const capitalOutlays = [
    {
      _id: "co1",
      category: {
        _id: "cat1",
        categoryName: "ACQUISITION OF FURNITURE & FIXTURES AND BOOKS"
      },
      sublineItem: "Furniture Fixture and Books",
      accountingTitle: "Books",
      uacsCode: "1-06-07-020",
      particulars: "Acquisition of reference books and library materials",
      income: "150000",
      subsidy: "25000",
      status: "Not Submitted"
    },
    {
      _id: "co2", 
      category: {
        _id: "cat2",
        categoryName: "ACQUISITION OF MACHINERY AND EQUIPMENT"
      },
      sublineItem: "Office Equipment",
      accountingTitle: "Office Equipment",
      uacsCode: "1-06-03-010",
      particulars: "Purchase of office equipment for administrative use",
      income: "0",
      subsidy: "500000",
      status: "Not Submitted"
    },
    {
      _id: "co3",
      category: {
        _id: "cat1",
        categoryName: "ACQUISITION OF FURNITURE & FIXTURES AND BOOKS"
      },
      sublineItem: "Furniture Fixture and Books",
      accountingTitle: "Furniture and Fixtures",
      uacsCode: "1-06-05-010",
      particulars: "Office furniture for new building",
      income: "0",
      subsidy: "300000",
      status: "Not Submitted"
    },
    {
      _id: "co4",
      category: {
        _id: "cat2",
        categoryName: "ACQUISITION OF MACHINERY AND EQUIPMENT"
      },
      sublineItem: "ICT Equipment",
      accountingTitle: "Information and Communication Technology Equipment",
      uacsCode: "1-06-03-030",
      particulars: "Computer systems and networking equipment",
      income: "0",
      subsidy: "800000",
      status: "Not Submitted"
    },
    {
      _id: "co5",
      category: {
        _id: "cat3",
        categoryName: "ACQUISITION OF TRANSPORTATION EQUIPMENT"
      },
      sublineItem: "Motor Vehicles",
      accountingTitle: "Motor Vehicles",
      uacsCode: "1-06-04-010",
      particulars: "Service vehicles for field operations",
      income: "0",
      subsidy: "1200000",
      status: "Not Submitted"
    }
  ];
  
  const response = {
    capitalOutlays,
    status: "Draft"
  };
  
  console.log('📤 Sending Capital Outlay response with', capitalOutlays.length, 'entries');
  res.status(200).json(response);
});

app.get('/capital-outlay-list', (req, res) => {
  console.log('🏗️ Capital Outlay List endpoint called');
  
  const capitalOutlayList = [
    {
      _id: "co1",
      category: "ACQUISITION OF FURNITURE & FIXTURES AND BOOKS",
      sublineItem: "Furniture Fixture and Books",
      accountingTitle: "Books",
      uacsCode: "1-06-07-020",
      amount: "175000"
    },
    {
      _id: "co2",
      category: "ACQUISITION OF MACHINERY AND EQUIPMENT", 
      sublineItem: "Office Equipment",
      accountingTitle: "Office Equipment",
      uacsCode: "1-06-03-010",
      amount: "500000"
    },
    {
      _id: "co3",
      category: "ACQUISITION OF TRANSPORTATION EQUIPMENT",
      sublineItem: "Motor Vehicles",
      accountingTitle: "Motor Vehicles", 
      uacsCode: "1-06-04-010",
      amount: "1200000"
    }
  ];
  
  console.log('📤 Sending', capitalOutlayList.length, 'Capital Outlay list records');
  res.status(200).json({ capitalOutlayList });
});

// ==================== CATEGORIES ENDPOINTS ====================
app.get('/categories', (req, res) => {
  console.log('📂 Categories endpoint called');
  
  const categories = [
    {
      _id: "cat1",
      categoryName: "ACQUISITION OF FURNITURE & FIXTURES AND BOOKS",
      order: 1
    },
    {
      _id: "cat2", 
      categoryName: "ACQUISITION OF MACHINERY AND EQUIPMENT",
      order: 2
    },
    {
      _id: "cat3",
      categoryName: "ACQUISITION OF TRANSPORTATION EQUIPMENT",
      order: 3
    },
    {
      _id: "cat4",
      categoryName: "CONSTRUCTION/ACQUISITION OF BUILDINGS AND OTHER STRUCTURES", 
      order: 4
    },
    {
      _id: "cat5",
      categoryName: "ACQUISITION OF LAND",
      order: 5
    }
  ];
  
  console.log('📤 Sending', categories.length, 'categories');
  res.status(200).json({ categories });
});

// ==================== SUBLINE ITEMS ENDPOINTS ====================
app.get('/subline-items', (req, res) => {
  console.log('📋 Subline Items endpoint called for category:', req.query.category);

  const category = req.query.category;
  let sublineItems = [];

  // Clean the category name (remove extra quotes and normalize)
  const cleanCategory = category.replace(/^["']|["']$/g, '').trim();

  switch(cleanCategory) {
    case "ACQUISITION OF FURNITURE & FIXTURES AND BOOKS":
      sublineItems = ["Furniture Fixture and Books"];
      break;
    case "ACQUISITION OF MACHINERY AND EQUIPMENT":
      sublineItems = ["Office Equipment", "ICT Equipment", "Agricultural Equipment", "Other Machinery and Equipment"];
      break;
    case "ACQUISITION OF TRANSPORTATION EQUIPMENT":
      sublineItems = ["Motor Vehicles", "Watercrafts", "Aircrafts", "Other Transportation Equipment"];
      break;
    case "CONSTRUCTION/ACQUISITION OF BUILDINGS AND OTHER STRUCTURES":
    case "Construction/Repair/Improvement of Buildings & Other Structures and Facilities":
      sublineItems = ["Buildings", "Other Structures", "Repairs and Maintenance"];
      break;
    case "ACQUISITION OF LAND":
      sublineItems = ["Land"];
      break;
    case "LAND IMPROVEMENTS":
      sublineItems = ["Land Improvements"];
      break;
    default:
      console.log("⚠️ Unknown category:", cleanCategory);
      sublineItems = [];
  }

  console.log('📤 Sending', sublineItems.length, 'subline items for', cleanCategory);
  res.status(200).json({ sublineItems });
});

// ==================== ACCOUNTING TITLES ENDPOINTS ====================
app.get('/accounting-titles', (req, res) => {
  console.log('📊 Accounting Titles endpoint called for subline:', req.query.sublineItem);

  const sublineItem = req.query.sublineItem;
  let accountingTitles = [];

  switch(sublineItem) {
    case "Furniture Fixture and Books":
      accountingTitles = [
        { accountingTitle: "Books", uacsCode: "1-06-07-020" },
        { accountingTitle: "Furniture and Fixtures", uacsCode: "1-06-05-010" },
        { accountingTitle: "Office Furniture", uacsCode: "1-06-05-020" }
      ];
      break;
    case "Land":
      accountingTitles = [
        { accountingTitle: "Land", uacsCode: "1-06-01-010" }
      ];
      break;
    case "Buildings":
      accountingTitles = [
        { accountingTitle: "Office Buildings", uacsCode: "1-06-02-010" },
        { accountingTitle: "School Buildings", uacsCode: "1-06-02-020" },
        { accountingTitle: "Hospital Buildings", uacsCode: "1-06-02-030" }
      ];
      break;
    case "Other Structures":
      accountingTitles = [
        { accountingTitle: "Roads and Bridges", uacsCode: "1-06-02-040" },
        { accountingTitle: "Water Supply Systems", uacsCode: "1-06-02-050" }
      ];
      break;
    case "Office Equipment":
      accountingTitles = [
        { accountingTitle: "Office Equipment", uacsCode: "1-06-03-010" },
        { accountingTitle: "Printing Equipment", uacsCode: "1-06-03-020" }
      ];
      break;
    case "ICT Equipment":
      accountingTitles = [
        { accountingTitle: "Information and Communication Technology Equipment", uacsCode: "1-06-03-030" },
        { accountingTitle: "Computer Software", uacsCode: "1-06-03-040" }
      ];
      break;
    case "Motor Vehicles":
      accountingTitles = [
        { accountingTitle: "Motor Vehicles", uacsCode: "1-06-04-010" },
        { accountingTitle: "Trucks and Trailers", uacsCode: "1-06-04-020" }
      ];
      break;
    case "Watercrafts":
      accountingTitles = [
        { accountingTitle: "Watercrafts", uacsCode: "1-06-04-030" }
      ];
      break;
    case "Aircrafts":
      accountingTitles = [
        { accountingTitle: "Aircrafts", uacsCode: "1-06-04-040" }
      ];
      break;
    case "Other Transportation Equipment":
      accountingTitles = [
        { accountingTitle: "Other Transportation Equipment", uacsCode: "1-06-04-990" }
      ];
      break;
    case "Agricultural Equipment":
      accountingTitles = [
        { accountingTitle: "Agricultural Equipment", uacsCode: "1-06-03-050" },
        { accountingTitle: "Farm Machinery", uacsCode: "1-06-03-060" }
      ];
      break;
    case "Other Machinery and Equipment":
      accountingTitles = [
        { accountingTitle: "Other Machinery and Equipment", uacsCode: "1-06-03-990" }
      ];
      break;
    case "Repairs and Maintenance":
      accountingTitles = [
        { accountingTitle: "Repairs and Maintenance - Buildings", uacsCode: "1-06-02-060" },
        { accountingTitle: "Repairs and Maintenance - Equipment", uacsCode: "1-06-03-070" }
      ];
      break;
    case "Land Improvements":
      accountingTitles = [
        { accountingTitle: "Land Improvements", uacsCode: "1-06-01-020" }
      ];
      break;
    case "Furniture and Fixtures":
      accountingTitles = [
        { accountingTitle: "Furniture and Fixtures", uacsCode: "1-06-05-010" }
      ];
      break;
    default:
      console.log("⚠️ Unknown subline item:", sublineItem);
      accountingTitles = [
        { accountingTitle: "Other Capital Outlay", uacsCode: "1-06-99-990" }
      ];
  }

  console.log('📤 Sending', accountingTitles.length, 'accounting titles for', sublineItem);
  res.status(200).json({ accountingTitles });
});

// ==================== INCOME ENDPOINTS ====================
app.get('/income-subcategories', (req, res) => {
  console.log('💰 Income Subcategories endpoint called');

  const incomeSubcategories = [
    {
      _id: "i1",
      category: "MOOE Income",
      subcategory: "Service Income",
      amount: "100000",
      description: "Income from services rendered"
    },
    {
      _id: "i2",
      category: "Capital Outlay Income",
      subcategory: "Equipment Income",
      amount: "200000",
      description: "Income from equipment sales"
    },
    {
      _id: "i3",
      category: "MOOE Income",
      subcategory: "Rental Income",
      amount: "50000",
      description: "Income from property rentals"
    },
    {
      _id: "i4",
      category: "Capital Outlay Income",
      subcategory: "Asset Disposal Income",
      amount: "75000",
      description: "Income from asset disposal"
    }
  ];

  console.log('📤 Sending', incomeSubcategories.length, 'Income subcategories');
  res.status(200).json({ incomeSubcategories });
});

// ==================== BUDGETARY SUPPORT ENDPOINTS ====================
app.get('/budgetary-support', (req, res) => {
  console.log('🏛️ Budgetary Support endpoint called');

  const budgetarySupport = [
    {
      _id: "b1",
      category: "MOOE Subsidy",
      subcategory: "Operating Subsidy",
      amount: "500000",
      description: "Government subsidy for operations"
    },
    {
      _id: "b2",
      category: "Capital Outlay Subsidy",
      subcategory: "Equipment Subsidy",
      amount: "1000000",
      description: "Government subsidy for equipment"
    },
    {
      _id: "b3",
      category: "MOOE Subsidy",
      subcategory: "Maintenance Subsidy",
      amount: "300000",
      description: "Government subsidy for maintenance"
    },
    {
      _id: "b4",
      category: "Capital Outlay Subsidy",
      subcategory: "Infrastructure Subsidy",
      amount: "2000000",
      description: "Government subsidy for infrastructure"
    }
  ];

  console.log('📤 Sending', budgetarySupport.length, 'Budgetary support records');
  res.status(200).json({ budgetarySupport });
});

// ==================== SETTINGS ENDPOINTS ====================
app.get('/settings/active', (req, res) => {
  console.log('⚙️ Settings endpoint called');

  const settings = {
    fiscalYear: "2026",
    budgetType: "Initial",
    region: "Region IV-A",
    department: "National Irrigation Administration",
    status: "Active"
  };

  console.log('📤 Sending settings:', settings);
  res.status(200).json(settings);
});

// ==================== CRUD ENDPOINTS ====================
// Capital Outlay CRUD
app.post('/capital-outlays', (req, res) => {
  console.log('💾 Capital Outlay create called');
  console.log('📥 Request body:', req.body);
  res.status(200).json({
    message: 'Capital Outlay created successfully!',
    _id: 'co_' + Date.now()
  });
});

app.put('/capital-outlays/:id', (req, res) => {
  console.log('💾 Capital Outlay update called for ID:', req.params.id);
  console.log('📥 Request body:', req.body);
  res.status(200).json({ message: 'Capital Outlay updated successfully!' });
});

app.delete('/capital-outlays/:id', (req, res) => {
  console.log('🗑️ Capital Outlay delete called for ID:', req.params.id);
  res.status(200).json({ message: 'Capital Outlay deleted successfully!' });
});

// MOOE CRUD
app.post('/mooe-save', (req, res) => {
  console.log('💾 MOOE save called');
  console.log('📥 Request body:', req.body);
  res.status(200).json({ message: 'MOOE data saved successfully!' });
});

app.put('/mooe-data/:id', (req, res) => {
  console.log('💾 MOOE update called for ID:', req.params.id);
  console.log('📥 Request body:', req.body);
  res.status(200).json({ message: 'MOOE data updated successfully!' });
});

app.delete('/mooe-data/:id', (req, res) => {
  console.log('🗑️ MOOE delete called for ID:', req.params.id);
  res.status(200).json({ message: 'MOOE data deleted successfully!' });
});

// ==================== TEST ENDPOINTS ====================
app.get('/test', (req, res) => {
  console.log('🧪 Test endpoint called');
  res.status(200).json({
    message: 'Complete server is working!',
    timestamp: new Date().toISOString(),
    endpoints: [
      'GET /getpersonnels',
      'GET /mooe-data',
      'GET /mooe-list',
      'GET /capital-outlays',
      'GET /capital-outlay-list',
      'GET /categories',
      'GET /subline-items',
      'GET /accounting-titles',
      'GET /income-subcategories',
      'GET /budgetary-support',
      'GET /settings/active',
      'POST /capital-outlays',
      'PUT /capital-outlays/:id',
      'DELETE /capital-outlays/:id',
      'POST /mooe-save',
      'PUT /mooe-data/:id',
      'DELETE /mooe-data/:id'
    ]
  });
});

// ==================== ERROR HANDLING ====================
app.use((req, res) => {
  console.log('❌ 404 - Endpoint not found:', req.method, req.path);
  res.status(404).json({
    error: 'Endpoint not found',
    method: req.method,
    path: req.path,
    availableEndpoints: [
      'GET /getpersonnels',
      'GET /mooe-data',
      'GET /capital-outlays',
      'GET /categories',
      'GET /test'
    ]
  });
});

// ==================== START SERVER ====================
app.listen(PORT, () => {
  console.log('🚀 Complete BUDGET-FMIS Mock Server running at http://localhost:' + PORT);
  console.log('📋 Available endpoints:');
  console.log('   👥 Personnel: /getpersonnels');
  console.log('   📊 MOOE: /mooe-data, /mooe-list');
  console.log('   🏗️ Capital Outlay: /capital-outlays, /capital-outlay-list');
  console.log('   📂 Categories: /categories');
  console.log('   📋 Subline Items: /subline-items');
  console.log('   📊 Accounting Titles: /accounting-titles');
  console.log('   💰 Income: /income-subcategories');
  console.log('   🏛️ Budgetary Support: /budgetary-support');
  console.log('   ⚙️ Settings: /settings/active');
  console.log('   🧪 Test: /test');
  console.log('');
  console.log('🎯 Ready to serve all BUDGET-FMIS requests!');
});
